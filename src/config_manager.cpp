#include "../include/common.h"
#include <fstream>
#include <iostream>
#include <sstream>
#include <cctype>

// 简单的JSON解析函数
bool parseJsonValue(const std::string& jsonStr, const std::string& key, std::string& value) {
    std::string searchKey = "\"" + key + "\"";
    size_t pos = jsonStr.find(searchKey);
    if (pos == std::string::npos) {
        return false;
    }
    
    // 找到键后，查找冒号
    pos = jsonStr.find(":", pos);
    if (pos == std::string::npos) {
        return false;
    }
    
    // 跳过冒号和空白
    pos++;
    while (pos < jsonStr.length() && (jsonStr[pos] == ' ' || jsonStr[pos] == '\t')) {
        pos++;
    }
    
    // 检查值类型
    if (pos >= jsonStr.length()) {
        return false;
    }
    
    if (jsonStr[pos] == '"') {
        // 字符串值
        size_t startPos = pos + 1;
        size_t endPos = jsonStr.find("\"", startPos);
        if (endPos == std::string::npos) {
            return false;
        }
        value = jsonStr.substr(startPos, endPos - startPos);
    } else if (jsonStr[pos] == '{' || jsonStr[pos] == '[') {
        // 对象或数组，不支持
        return false;
    } else {
        // 数字或布尔值
        size_t endPos = jsonStr.find_first_of(",}\n", pos);
        if (endPos == std::string::npos) {
            value = jsonStr.substr(pos);
        } else {
            value = jsonStr.substr(pos, endPos - pos);
        }
        // 去除尾部空白
        while (!value.empty() && (value.back() == ' ' || value.back() == '\t' || value.back() == '\r')) {
            value.pop_back();
        }
    }
    
    return true;
}

// 解析数字值
template<typename T>
bool parseJsonNumber(const std::string& jsonStr, const std::string& key, T& value) {
    std::string strValue;
    if (!parseJsonValue(jsonStr, key, strValue)) {
        return false;
    }
    
    try {
        if constexpr (std::is_same_v<T, int>) {
            value = std::stoi(strValue);
        } else if constexpr (std::is_same_v<T, double>) {
            value = std::stod(strValue);
        } else {
            return false;
        }
        return true;
    } catch (...) {
        return false;
    }
}

// 初始化Config命名空间中的变量
int Config::CAMERA_COUNT = 4;
int Config::CAMERA_WIDTH = 640;
int Config::CAMERA_HEIGHT = 480;
int Config::TARGET_FPS = 15;
double Config::MIN_CRACK_WIDTH = 0.1;
double Config::MIN_DAMAGE_SIZE = 10.0;
int Config::MAX_QUEUE_SIZE = 30;
int Config::PROCESS_TIMEOUT = 500;
int Config::RTSP_PORT = 8554;
std::string Config::RTSP_PATH = "/live";

// 初始化配置
bool Config::initializeConfig(const std::string& configPath) {
    return ConfigManager::getInstance().loadConfig(configPath);
}

// ConfigManager单例实现
ConfigManager& ConfigManager::getInstance() {
    static ConfigManager instance;
    return instance;
}

// 加载配置文件
bool ConfigManager::loadConfig(const std::string& configPath) {
    if (!parseJsonFile(configPath)) {
        std::cerr << "无法加载配置文件: " << configPath << std::endl;
        return false;
    }
    
    // 更新Config命名空间中的变量
    Config::CAMERA_COUNT = cameraCount_;
    Config::CAMERA_WIDTH = cameraWidth_;
    Config::CAMERA_HEIGHT = cameraHeight_;
    Config::TARGET_FPS = targetFPS_;
    Config::MIN_CRACK_WIDTH = minCrackWidth_;
    Config::MIN_DAMAGE_SIZE = minDamageSize_;
    Config::MAX_QUEUE_SIZE = maxQueueSize_;
    Config::PROCESS_TIMEOUT = processTimeout_;
    Config::RTSP_PORT = rtspServerPort_;
    Config::RTSP_PATH = rtspPath_;
    
    return true;
}

// 解析JSON文件
bool ConfigManager::parseJsonFile(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        std::cerr << "无法打开配置文件: " << filePath << std::endl;
        return false;
    }
    
    std::stringstream buffer;
    buffer << file.rdbuf();
    std::string jsonStr = buffer.str();
    
    // 解析摄像头配置
    std::string cameraSection;
    size_t cameraStart = jsonStr.find("\"camera\"");
    if (cameraStart != std::string::npos) {
        cameraStart = jsonStr.find("{", cameraStart);
        if (cameraStart != std::string::npos) {
            size_t cameraEnd = jsonStr.find("}", cameraStart);
            if (cameraEnd != std::string::npos) {
                cameraSection = jsonStr.substr(cameraStart, cameraEnd - cameraStart + 1);
                
                parseJsonNumber(cameraSection, "count", cameraCount_);
                parseJsonNumber(cameraSection, "width", cameraWidth_);
                parseJsonNumber(cameraSection, "height", cameraHeight_);
                parseJsonNumber(cameraSection, "target_fps", targetFPS_);
                parseJsonNumber(cameraSection, "max_queue_size", maxQueueSize_);
            }
        }
    }
    
    // 解析检测配置
    std::string detectionSection;
    size_t detectionStart = jsonStr.find("\"detection\"");
    if (detectionStart != std::string::npos) {
        detectionStart = jsonStr.find("{", detectionStart);
        if (detectionStart != std::string::npos) {
            size_t detectionEnd = jsonStr.find("}", detectionStart);
            if (detectionEnd != std::string::npos) {
                detectionSection = jsonStr.substr(detectionStart, detectionEnd - detectionStart + 1);
                
                parseJsonNumber(detectionSection, "min_crack_width_mm", minCrackWidth_);
                parseJsonNumber(detectionSection, "min_damage_size_mm", minDamageSize_);
                parseJsonNumber(detectionSection, "process_timeout_ms", processTimeout_);
            }
        }
    }
    
    // 解析推流配置
    std::string streamingSection;
    size_t streamingStart = jsonStr.find("\"streaming\"");
    if (streamingStart != std::string::npos) {
        streamingStart = jsonStr.find("{", streamingStart);
        if (streamingStart != std::string::npos) {
            // 找到streaming配置块的结束位置（需要处理嵌套的大括号）
            int braceCount = 0;
            size_t streamingEnd = streamingStart;
            for (size_t i = streamingStart; i < jsonStr.length(); ++i) {
                if (jsonStr[i] == '{') braceCount++;
                else if (jsonStr[i] == '}') {
                    braceCount--;
                    if (braceCount == 0) {
                        streamingEnd = i;
                        break;
                    }
                }
            }

            if (streamingEnd > streamingStart) {
                streamingSection = jsonStr.substr(streamingStart, streamingEnd - streamingStart + 1);

                // 解析基本配置
                std::string enabledStr;
                if (parseJsonValue(streamingSection, "enabled", enabledStr)) {
                    streamingEnabled_ = (enabledStr == "true");
                }
                parseJsonValue(streamingSection, "mode", streamingMode_);
                parseJsonValue(streamingSection, "server_type", streamingServerType_);

                // 解析并验证RTSP服务器地址
                std::string tempAddress;
                if (parseJsonValue(streamingSection, "rtsp_server_address", tempAddress)) {
                    if (validateIpAddress(tempAddress)) {
                        rtspServerAddress_ = tempAddress;
                    } else {
                        std::cerr << "警告: 无效的RTSP服务器地址 '" << tempAddress << "'，使用默认值" << std::endl;
                    }
                }

                // 解析并验证RTSP服务器端口
                int tempPort;
                if (parseJsonNumber(streamingSection, "rtsp_server_port", tempPort)) {
                    if (validatePort(tempPort)) {
                        rtspServerPort_ = tempPort;
                    } else {
                        std::cerr << "警告: 无效的RTSP服务器端口 " << tempPort << "，使用默认值" << std::endl;
                    }
                }
                parseJsonValue(streamingSection, "stream_key", streamingKey_);

                // 解析嵌套的视频编码配置
                std::string videoEncodingSection;
                size_t videoEncodingStart = streamingSection.find("\"video_encoding\"");
                if (videoEncodingStart != std::string::npos) {
                    videoEncodingStart = streamingSection.find("{", videoEncodingStart);
                    if (videoEncodingStart != std::string::npos) {
                        int braceCount = 0;
                        size_t videoEncodingEnd = videoEncodingStart;
                        for (size_t i = videoEncodingStart; i < streamingSection.length(); ++i) {
                            if (streamingSection[i] == '{') braceCount++;
                            else if (streamingSection[i] == '}') {
                                braceCount--;
                                if (braceCount == 0) {
                                    videoEncodingEnd = i;
                                    break;
                                }
                            }
                        }

                        if (videoEncodingEnd > videoEncodingStart) {
                            videoEncodingSection = streamingSection.substr(videoEncodingStart, videoEncodingEnd - videoEncodingStart + 1);

                            // 从video_encoding子对象中解析参数
                            parseJsonValue(videoEncodingSection, "codec", videoCodec_);
                            parseJsonNumber(videoEncodingSection, "width", videoWidth_);
                            parseJsonNumber(videoEncodingSection, "height", videoHeight_);
                            parseJsonNumber(videoEncodingSection, "fps", videoFPS_);
                            parseJsonNumber(videoEncodingSection, "bitrate", videoBitrate_);
                            parseJsonValue(videoEncodingSection, "preset", videoPreset_);
                            parseJsonValue(videoEncodingSection, "profile", videoProfile_);
                        }
                    }
                }

                // 解析嵌套的连接配置
                std::string connectionSection;
                size_t connectionStart = streamingSection.find("\"connection\"");
                if (connectionStart != std::string::npos) {
                    connectionStart = streamingSection.find("{", connectionStart);
                    if (connectionStart != std::string::npos) {
                        int braceCount = 0;
                        size_t connectionEnd = connectionStart;
                        for (size_t i = connectionStart; i < streamingSection.length(); ++i) {
                            if (streamingSection[i] == '{') braceCount++;
                            else if (streamingSection[i] == '}') {
                                braceCount--;
                                if (braceCount == 0) {
                                    connectionEnd = i;
                                    break;
                                }
                            }
                        }

                        if (connectionEnd > connectionStart) {
                            connectionSection = streamingSection.substr(connectionStart, connectionEnd - connectionStart + 1);

                            // 从connection子对象中解析参数
                            parseJsonNumber(connectionSection, "connect_timeout_ms", connectTimeoutMs_);
                            parseJsonNumber(connectionSection, "reconnect_interval_ms", reconnectIntervalMs_);
                            parseJsonNumber(connectionSection, "max_retries", maxRetries_);
                        }
                    }
                }

                // 解析嵌套的质量控制配置
                std::string qualityControlSection;
                size_t qualityControlStart = streamingSection.find("\"quality_control\"");
                if (qualityControlStart != std::string::npos) {
                    qualityControlStart = streamingSection.find("{", qualityControlStart);
                    if (qualityControlStart != std::string::npos) {
                        int braceCount = 0;
                        size_t qualityControlEnd = qualityControlStart;
                        for (size_t i = qualityControlStart; i < streamingSection.length(); ++i) {
                            if (streamingSection[i] == '{') braceCount++;
                            else if (streamingSection[i] == '}') {
                                braceCount--;
                                if (braceCount == 0) {
                                    qualityControlEnd = i;
                                    break;
                                }
                            }
                        }

                        if (qualityControlEnd > qualityControlStart) {
                            qualityControlSection = streamingSection.substr(qualityControlStart, qualityControlEnd - qualityControlStart + 1);

                            // 从quality_control子对象中解析参数
                            std::string adaptiveStr;
                            if (parseJsonValue(qualityControlSection, "adaptive_bitrate", adaptiveStr)) {
                                adaptiveBitrate_ = (adaptiveStr == "true");
                            }
                            parseJsonNumber(qualityControlSection, "min_bitrate", minBitrate_);
                            parseJsonNumber(qualityControlSection, "max_bitrate", maxBitrate_);
                            parseJsonNumber(qualityControlSection, "frame_drop_threshold", frameDropThreshold_);
                            parseJsonNumber(qualityControlSection, "buffer_size", bufferSize_);
                            parseJsonNumber(qualityControlSection, "max_queue_size", maxQueueSize_);
                        }
                    }
                }
            }
        }
    }
    
    return true;
}

// 验证IP地址格式
bool ConfigManager::validateIpAddress(const std::string& ip) const {
    if (ip.empty()) {
        return false;
    }

    // 简单的IP地址格式验证
    std::istringstream iss(ip);
    std::string segment;
    int segmentCount = 0;

    while (std::getline(iss, segment, '.')) {
        segmentCount++;
        if (segmentCount > 4) {
            return false;
        }

        // 检查段是否为数字
        if (segment.empty() || segment.length() > 3) {
            return false;
        }

        for (char c : segment) {
            if (!std::isdigit(c)) {
                return false;
            }
        }

        // 检查数值范围
        int value = std::stoi(segment);
        if (value < 0 || value > 255) {
            return false;
        }

        // 检查前导零
        if (segment.length() > 1 && segment[0] == '0') {
            return false;
        }
    }

    return segmentCount == 4;
}

// 验证端口号
bool ConfigManager::validatePort(int port) const {
    return port > 0 && port <= 65535;
}

// 自动生成推流URL
std::string ConfigManager::getStreamingPushUrl() const {
    return "rtsp://" + rtspServerAddress_ + ":" + std::to_string(rtspServerPort_) + rtspPath_;
}

// 自动生成观看URL
std::string ConfigManager::getStreamingViewUrl() const {
    return "rtsp://" + rtspServerAddress_ + ":" + std::to_string(rtspServerPort_) + rtspPath_;
}
